# NOTE: Requires **VS2019 16.7** or later

# Rules from '5.0' release with 'Default' analysis mode
# Description: Rules with enabled-by-default state from '5.0' release with 'Default' analysis mode. Rules that are first released in a version later than '5.0' are disabled.

is_global = true

global_level = -100


# CA1311: Specify a culture or use an invariant version
dotnet_diagnostic.CA1311.severity = none

# CA1418: Use valid platform string
dotnet_diagnostic.CA1418.severity = none

# CA1419: Provide a parameterless constructor that is as visible as the containing type for concrete types derived from 'System.Runtime.InteropServices.SafeHandle'
dotnet_diagnostic.CA1419.severity = none

# CA1420: Property, type, or attribute requires runtime marshalling
dotnet_diagnostic.CA1420.severity = none

# CA1421: This method uses runtime marshalling even when the 'DisableRuntimeMarshallingAttribute' is applied
dotnet_diagnostic.CA1421.severity = none

# CA1422: Validate platform compatibility
dotnet_diagnostic.CA1422.severity = none

# CA1510: Use ArgumentNullException throw helper
dotnet_diagnostic.CA1510.severity = none

# CA1511: Use ArgumentException throw helper
dotnet_diagnostic.CA1511.severity = none

# CA1512: Use ArgumentOutOfRangeException throw helper
dotnet_diagnostic.CA1512.severity = none

# CA1513: Use ObjectDisposedException throw helper
dotnet_diagnostic.CA1513.severity = none

# CA1514: Avoid redundant length argument
dotnet_diagnostic.CA1514.severity = none

# CA1727: Use PascalCase for named placeholders
dotnet_diagnostic.CA1727.severity = none

# CA1839: Use 'Environment.ProcessPath'
dotnet_diagnostic.CA1839.severity = none

# CA1840: Use 'Environment.CurrentManagedThreadId'
dotnet_diagnostic.CA1840.severity = none

# CA1841: Prefer Dictionary.Contains methods
dotnet_diagnostic.CA1841.severity = none

# CA1842: Do not use 'WhenAll' with a single task
dotnet_diagnostic.CA1842.severity = none

# CA1843: Do not use 'WaitAll' with a single task
dotnet_diagnostic.CA1843.severity = none

# CA1844: Provide memory-based overrides of async methods when subclassing 'Stream'
dotnet_diagnostic.CA1844.severity = none

# CA1845: Use span-based 'string.Concat'
dotnet_diagnostic.CA1845.severity = none

# CA1846: Prefer 'AsSpan' over 'Substring'
dotnet_diagnostic.CA1846.severity = none

# CA1847: Use char literal for a single character lookup
dotnet_diagnostic.CA1847.severity = none

# CA1848: Use the LoggerMessage delegates
dotnet_diagnostic.CA1848.severity = none

# CA1850: Prefer static 'HashData' method over 'ComputeHash'
dotnet_diagnostic.CA1850.severity = none

# CA1852: Seal internal types
dotnet_diagnostic.CA1852.severity = none

# CA1853: Unnecessary call to 'Dictionary.ContainsKey(key)'
dotnet_diagnostic.CA1853.severity = none

# CA1854: Prefer the 'IDictionary.TryGetValue(TKey, out TValue)' method
dotnet_diagnostic.CA1854.severity = none

# CA1855: Prefer 'Clear' over 'Fill'
dotnet_diagnostic.CA1855.severity = none

# CA1856: Incorrect usage of ConstantExpected attribute
dotnet_diagnostic.CA1856.severity = none

# CA1857: A constant is expected for the parameter
dotnet_diagnostic.CA1857.severity = none

# CA1858: Use 'StartsWith' instead of 'IndexOf'
dotnet_diagnostic.CA1858.severity = none

# CA1859: Use concrete types when possible for improved performance
dotnet_diagnostic.CA1859.severity = none

# CA1860: Avoid using 'Enumerable.Any()' extension method
dotnet_diagnostic.CA1860.severity = none

# CA1861: Avoid constant arrays as arguments
dotnet_diagnostic.CA1861.severity = none

# CA1862: Use the 'StringComparison' method overloads to perform case-insensitive string comparisons
dotnet_diagnostic.CA1862.severity = none

# CA1863: Use 'CompositeFormat'
dotnet_diagnostic.CA1863.severity = none

# CA1864: Prefer the 'IDictionary.TryAdd(TKey, TValue)' method
dotnet_diagnostic.CA1864.severity = none

# CA1865: Use char overload
dotnet_diagnostic.CA1865.severity = none

# CA1866: Use char overload
dotnet_diagnostic.CA1866.severity = none

# CA1868: Unnecessary call to 'Contains(item)'
dotnet_diagnostic.CA1868.severity = none

# CA1869: Cache and reuse 'JsonSerializerOptions' instances
dotnet_diagnostic.CA1869.severity = none

# CA1870: Use a cached 'SearchValues' instance
dotnet_diagnostic.CA1870.severity = none

# CA1871: Do not pass a nullable struct to 'ArgumentNullException.ThrowIfNull'
dotnet_diagnostic.CA1871.severity = none

# CA1872: Prefer 'Convert.ToHexString' and 'Convert.ToHexStringLower' over call chains based on 'BitConverter.ToString'
dotnet_diagnostic.CA1872.severity = none

# CA2017: Parameter count mismatch
dotnet_diagnostic.CA2017.severity = none

# CA2018: 'Buffer.BlockCopy' expects the number of bytes to be copied for the 'count' argument
dotnet_diagnostic.CA2018.severity = none

# CA2019: Improper 'ThreadStatic' field initialization
dotnet_diagnostic.CA2019.severity = none

# CA2020: Prevent behavioral change
dotnet_diagnostic.CA2020.severity = none

# CA2021: Do not call Enumerable.Cast<T> or Enumerable.OfType<T> with incompatible types
dotnet_diagnostic.CA2021.severity = none

# CA2022: Avoid inexact read with 'Stream.Read'
dotnet_diagnostic.CA2022.severity = none

# CA2250: Use 'ThrowIfCancellationRequested'
dotnet_diagnostic.CA2250.severity = none

# CA2251: Use 'string.Equals'
dotnet_diagnostic.CA2251.severity = none

# CA2252: This API requires opting into preview features
dotnet_diagnostic.CA2252.severity = none

# CA2253: Named placeholders should not be numeric values
dotnet_diagnostic.CA2253.severity = none

# CA2254: Template should be a static expression
dotnet_diagnostic.CA2254.severity = none

# CA2255: The 'ModuleInitializer' attribute should not be used in libraries
dotnet_diagnostic.CA2255.severity = none

# CA2256: All members declared in parent interfaces must have an implementation in a DynamicInterfaceCastableImplementation-attributed interface
dotnet_diagnostic.CA2256.severity = none

# CA2257: Members defined on an interface with the 'DynamicInterfaceCastableImplementationAttribute' should be 'static'
dotnet_diagnostic.CA2257.severity = none

# CA2258: Providing a 'DynamicInterfaceCastableImplementation' interface in Visual Basic is unsupported
dotnet_diagnostic.CA2258.severity = none

# CA2259: 'ThreadStatic' only affects static fields
dotnet_diagnostic.CA2259.severity = none

# CA2260: Use correct type parameter
dotnet_diagnostic.CA2260.severity = none

# CA2261: Do not use ConfigureAwaitOptions.SuppressThrowing with Task<TResult>
dotnet_diagnostic.CA2261.severity = none

# CA2262: Set 'MaxResponseHeadersLength' properly
dotnet_diagnostic.CA2262.severity = none

# CA2263: Prefer generic overload when type is known
dotnet_diagnostic.CA2263.severity = none

# CA2264: Do not pass a non-nullable value to 'ArgumentNullException.ThrowIfNull'
dotnet_diagnostic.CA2264.severity = none

# CA2265: Do not compare Span<T> to 'null' or 'default'
dotnet_diagnostic.CA2265.severity = none
