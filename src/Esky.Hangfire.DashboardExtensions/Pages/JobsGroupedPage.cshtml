@* Generator: Template TypeVisibility: Internal GeneratePrettyNames: True *@
@using System.Linq
@using Hangfire.Dashboard
@using Hangfire.Dashboard.Pages
@using Esky.Hangfire.DashboardExtensions
@inherits global::Hangfire.Dashboard.RazorPage
@{
    Layout = new LayoutPage("Jobs grouped");

    var sw = System.Diagnostics.Stopwatch.StartNew();

    var jobFilter = this.CreateJobFilterFromQueryString();

    GroupedJobsModel model = this.extendedMonitoringApi.GetGroupedJobs(jobFilter);

    sw.Stop();

    Func<string, string> stateNameToClass = (string s) =>
    {
        switch (s)
        {
            case "Enqueued": return "metric-info highlighted";
            case "Scheduled": return "metric-info highlighted";
            case "Processing": return "metric-warning";
            case "Succeeded": return "metric-default";
            case "Failed": return "metric-danger highlighted";
            default: return "metric-default";
        }
    };
}

<div class="row">
    <div class="col-md-3">
        @Html.JobsSidebar()
    </div>
    <div class="col-md-9">
        <h1 class="page-header">Jobs grouped by type/method</h1>

            <div class="btn-toolbar btn-toolbar-top">
                <form method="get" class="form-inline">
                    <div class="form-group">
                        From:
                        <input class="form-control" type="datetime" value="@jobFilter.DateFrom.ToString("yyyy-MM-dd HH:mm:ss")" name="dateFrom" />
                        To:
                        <input class="form-control" type="datetime" value="@jobFilter.DateTo.ToString("yyyy-MM-dd HH:mm:ss")" name="dateTo" />

                        Quick time range - last
                        <a href="#" onclick="$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-1, 'h').format('YYYY-MM-DD HH:mm:00')); return false;">1 hour</a> |
                        <a href="#" onclick="$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-4, 'h').format('YYYY-MM-DD HH:mm:00')); return false;">4 hours</a> |
                        <a href="#" onclick="$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-1, 'd').format('YYYY-MM-DD HH:mm:00')); return false;">1 day</a> |
                        <a href="#" onclick="$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-7, 'd').format('YYYY-MM-DD HH:mm:00')); return false;">7 days</a>


                    </div>
                    <div class="form-group">
                        Search:
                        <input class="form-control" type="text" value="@jobFilter.SearchPhrase" name="searchPhrase" />
                        State:
                        <select class="form-control" name="stateName">
                            @foreach (var option in new string[] { "", "Succeeded", "Processing", "Failed", "Deleted" })
                            {
                                <option value="@option" @(option == jobFilter.StateName ? "selected=\"selected\"" : "")>@option</option>
                            }
                        </select>
                    </div>

                    <div class="form-group">
                        <input type="submit" value="Filter" class="btn btn-default" />
                    </div>
                </form>
            </div>

            <div class="js-jobs-list">

                <div class="table-responsive">

                    <table class="table">
                        <thead>
                            <tr>
                                <th>
                                    Job
                                </th>
                                <th>
                                    State
                                </th>
                                <th>
                                    Count
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var jobGroup in model.JobGroups)
                            {
                                <tr class="js-jobs-list-row active">
                                    <td colspan="2">

                                        <strong>@jobGroup.Key.Method</strong>
                                        <small class="text-muted"> @jobGroup.Key.Type</small>

                                    </td>
                                    <th><span class="badge">@jobGroup.States.Sum(s => s.Count)</span></th>
                                    <td><a href="@Url.To($"/jobs/filtered?{jobFilter.OverrideParams(jobGroup.Key.Method, null).AsQueryString()}")">[List]</a></td>
                                </tr>

                                foreach (var state in jobGroup.States)
                                {
                                    <tr class="">
                                        <td></td>
                                        <td>@state.StateName</td>
                                        <td><span class="metric @stateNameToClass(state.StateName)">@state.Count</span></td>
                                        <td><a href="@Url.To($"/jobs/filtered?{jobFilter.OverrideParams(jobGroup.Key.Method, state.StateName).AsQueryString()}")">[List]</a></td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
            </div>

            <div>
                <h4>Query:</h4>
                
                <small class="text-muted">
                    @extendedMonitoringApi.GetJobFilterAsString(jobFilter)
                </small>
                <br />

                Elapsed: @sw.Elapsed.TotalSeconds.ToString("0.#") s
            </div>
        </div>
    </div>