#pragma warning disable 1591
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HangfireExtended.Dashboard
{
    using System;
    using System.Collections.Generic;
    
    #line 2 "..\..\Pages\JobsGroupedPage.cshtml"
    using System.Linq;
    
    #line default
    #line hidden
    using System.Text;
    
    #line 5 "..\..\Pages\JobsGroupedPage.cshtml"
    using Esky.Hangfire.DashboardExtensions;
    
    #line default
    #line hidden
    
    #line 3 "..\..\Pages\JobsGroupedPage.cshtml"
    using Hangfire.Dashboard;
    
    #line default
    #line hidden
    
    #line 4 "..\..\Pages\JobsGroupedPage.cshtml"
    using Hangfire.Dashboard.Pages;
    
    #line default
    #line hidden
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("RazorGenerator", "2.0.0.0")]
    internal partial class JobsGroupedPage : global::Hangfire.Dashboard.RazorPage
    {
#line hidden

        public override void Execute()
        {


WriteLiteral("\r\n");







            
            #line 7 "..\..\Pages\JobsGroupedPage.cshtml"
  
    Layout = new LayoutPage("Jobs grouped");

    var sw = System.Diagnostics.Stopwatch.StartNew();

    var jobFilter = this.CreateJobFilterFromQueryString();

    GroupedJobsModel model = this.extendedMonitoringApi.GetGroupedJobs(jobFilter);

    sw.Stop();

    Func<string, string> stateNameToClass = (string s) =>
    {
        switch (s)
        {
            case "Enqueued": return "metric-info highlighted";
            case "Scheduled": return "metric-info highlighted";
            case "Processing": return "metric-warning";
            case "Succeeded": return "metric-default";
            case "Failed": return "metric-danger highlighted";
            default: return "metric-default";
        }
    };


            
            #line default
            #line hidden
WriteLiteral("\r\n<div class=\"row\">\r\n    <div class=\"col-md-3\">\r\n        ");


            
            #line 34 "..\..\Pages\JobsGroupedPage.cshtml"
   Write(Html.JobsSidebar());

            
            #line default
            #line hidden
WriteLiteral(@"
    </div>
    <div class=""col-md-9"">
        <h1 class=""page-header"">Jobs grouped by type/method</h1>

            <div class=""btn-toolbar btn-toolbar-top"">
                <form method=""get"" class=""form-inline"">
                    <div class=""form-group"">
                        From:
                        <input class=""form-control"" type=""datetime"" value=""");


            
            #line 43 "..\..\Pages\JobsGroupedPage.cshtml"
                                                                      Write(jobFilter.DateFrom.ToString("yyyy-MM-dd HH:mm:ss"));

            
            #line default
            #line hidden
WriteLiteral("\" name=\"dateFrom\" />\r\n                        To:\r\n                        <input" +
" class=\"form-control\" type=\"datetime\" value=\"");


            
            #line 45 "..\..\Pages\JobsGroupedPage.cshtml"
                                                                      Write(jobFilter.DateTo.ToString("yyyy-MM-dd HH:mm:ss"));

            
            #line default
            #line hidden
WriteLiteral(@""" name=""dateTo"" />

                        Quick time range - last
                        <a href=""#"" onclick=""$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-1, 'h').format('YYYY-MM-DD HH:mm:00')); return false;"">1 hour</a> |
                        <a href=""#"" onclick=""$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-4, 'h').format('YYYY-MM-DD HH:mm:00')); return false;"">4 hours</a> |
                        <a href=""#"" onclick=""$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-1, 'd').format('YYYY-MM-DD HH:mm:00')); return false;"">1 day</a> |
                        <a href=""#"" onclick=""$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-7, 'd').format('YYYY-MM-DD HH:mm:00')); return false;"">7 days</a>


                    </div>
                    <div class=""form-group"">
                        Search:
                        <input class=""form-control"" type=""text"" value=""");


            
            #line 57 "..\..\Pages\JobsGroupedPage.cshtml"
                                                                  Write(jobFilter.SearchPhrase);

            
            #line default
            #line hidden
WriteLiteral("\" name=\"searchPhrase\" />\r\n                        State:\r\n                       " +
" <select class=\"form-control\" name=\"stateName\">\r\n");


            
            #line 60 "..\..\Pages\JobsGroupedPage.cshtml"
                             foreach (var option in new string[] { "", "Succeeded", "Processing", "Failed", "Deleted", "Scheduled" })
                            {

            
            #line default
            #line hidden
WriteLiteral("                                <option value=\"");


            
            #line 62 "..\..\Pages\JobsGroupedPage.cshtml"
                                          Write(option);

            
            #line default
            #line hidden
WriteLiteral("\" ");


            
            #line 62 "..\..\Pages\JobsGroupedPage.cshtml"
                                                    Write(option == jobFilter.StateName ? "selected=\"selected\"" : "");

            
            #line default
            #line hidden
WriteLiteral(">");


            
            #line 62 "..\..\Pages\JobsGroupedPage.cshtml"
                                                                                                                   Write(option);

            
            #line default
            #line hidden
WriteLiteral("</option>\r\n");


            
            #line 63 "..\..\Pages\JobsGroupedPage.cshtml"
                            }

            
            #line default
            #line hidden
WriteLiteral(@"                        </select>
                    </div>

                    <div class=""form-group"">
                        <input type=""submit"" value=""Filter"" class=""btn btn-default"" />
                    </div>
                </form>
            </div>

            <div class=""js-jobs-list"">

                <div class=""table-responsive"">

                    <table class=""table"">
                        <thead>
                            <tr>
                                <th>
                                    Job
                                </th>
                                <th>
                                    State
                                </th>
                                <th>
                                    Count
                                </th>
                            </tr>
                        </thead>
                        <tbody>
");


            
            #line 92 "..\..\Pages\JobsGroupedPage.cshtml"
                             foreach (var jobGroup in model.JobGroups)
                            {

            
            #line default
            #line hidden
WriteLiteral("                                <tr class=\"js-jobs-list-row active\">\r\n           " +
"                         <td colspan=\"2\">\r\n\r\n                                   " +
"     <strong>");


            
            #line 97 "..\..\Pages\JobsGroupedPage.cshtml"
                                           Write(jobGroup.Key.Method);

            
            #line default
            #line hidden
WriteLiteral("</strong>\r\n                                        <small class=\"text-muted\"> ");


            
            #line 98 "..\..\Pages\JobsGroupedPage.cshtml"
                                                              Write(jobGroup.Key.Type);

            
            #line default
            #line hidden
WriteLiteral("</small>\r\n\r\n                                    </td>\r\n                          " +
"          <th><span class=\"badge\">");


            
            #line 101 "..\..\Pages\JobsGroupedPage.cshtml"
                                                       Write(jobGroup.States.Sum(s => s.Count));

            
            #line default
            #line hidden
WriteLiteral("</span></th>\r\n                                    <td><a href=\"");


            
            #line 102 "..\..\Pages\JobsGroupedPage.cshtml"
                                            Write(Url.To($"/jobs/filtered?{jobFilter.OverrideParams(jobGroup.Key.Method, null).AsQueryString()}"));

            
            #line default
            #line hidden
WriteLiteral("\">[List]</a></td>\r\n                                </tr>\r\n");


            
            #line 104 "..\..\Pages\JobsGroupedPage.cshtml"

                                foreach (var state in jobGroup.States)
                                {

            
            #line default
            #line hidden
WriteLiteral("                                    <tr class=\"\">\r\n                              " +
"          <td></td>\r\n                                        <td>");


            
            #line 109 "..\..\Pages\JobsGroupedPage.cshtml"
                                       Write(state.StateName);

            
            #line default
            #line hidden
WriteLiteral("</td>\r\n                                        <td><span class=\"metric ");


            
            #line 110 "..\..\Pages\JobsGroupedPage.cshtml"
                                                           Write(stateNameToClass(state.StateName));

            
            #line default
            #line hidden
WriteLiteral("\">");


            
            #line 110 "..\..\Pages\JobsGroupedPage.cshtml"
                                                                                               Write(state.Count);

            
            #line default
            #line hidden
WriteLiteral("</span></td>\r\n                                        <td><a href=\"");


            
            #line 111 "..\..\Pages\JobsGroupedPage.cshtml"
                                                Write(Url.To($"/jobs/filtered?{jobFilter.OverrideParams(jobGroup.Key.Method, state.StateName).AsQueryString()}"));

            
            #line default
            #line hidden
WriteLiteral("\">[List]</a></td>\r\n                                    </tr>\r\n");


            
            #line 113 "..\..\Pages\JobsGroupedPage.cshtml"
                                }
                            }

            
            #line default
            #line hidden
WriteLiteral("                        </tbody>\r\n                    </table>\r\n                <" +
"/div>\r\n            </div>\r\n\r\n            <div>\r\n                <h4>Query:</h4>\r" +
"\n                \r\n                <small class=\"text-muted\">\r\n                 " +
"   ");


            
            #line 124 "..\..\Pages\JobsGroupedPage.cshtml"
               Write(extendedMonitoringApi.GetJobFilterAsString(jobFilter));

            
            #line default
            #line hidden
WriteLiteral("\r\n                </small>\r\n                <br />\r\n\r\n                Elapsed: ");


            
            #line 128 "..\..\Pages\JobsGroupedPage.cshtml"
                    Write(sw.Elapsed.TotalSeconds.ToString("0.#"));

            
            #line default
            #line hidden
WriteLiteral(" s\r\n            </div>\r\n        </div>\r\n    </div>");


        }
    }
}
#pragma warning restore 1591
