@* Generator: Template TypeVisibility: Internal GeneratePrettyNames: True *@
@using System.Linq
@using Hangfire.Dashboard.Pages
@using Hangfire.Dashboard.Resources
@using Esky.Hangfire.DashboardExtensions

@inherits global::Hangfire.Dashboard.RazorPage
@{
    Layout = new LayoutPage("Jobs filtered");
    var sw = System.Diagnostics.Stopwatch.StartNew();

    var jobFilter = this.CreateJobFilterFromQueryString();

    var jobs = extendedMonitoringApi.GetJobs(jobFilter);

    sw.Stop();

    Func<string, string> stateNameToClass = (string s) =>
    {
        switch (s)
        {
            case "Enqueued": return "metric-info highlighted";
            case "Scheduled": return "metric-info highlighted";
            case "Processing": return "metric-warning";
            case "Succeeded": return "metric-default";
            case "Failed": return "metric-danger highlighted";
            default: return "metric-default";
        }
    };
}

<div class="row">
    <div class="col-md-3">
        @Html.JobsSidebar()
    </div>
    <div class="col-md-9">
        <h1 class="page-header">Jobs filtered</h1>

        <div class="js-jobs-list">

            <div class="btn-toolbar btn-toolbar-top">
                <form method="get" class="form-inline">
                    <div class="form-group">
                        From:
                        <input class="form-control" type="datetime" value="@jobFilter.DateFrom.ToString("yyyy-MM-dd HH:mm:ss")" name="dateFrom" />
                        To:
                        <input class="form-control" type="datetime" value="@jobFilter.DateTo.ToString("yyyy-MM-dd HH:mm:ss")" name="dateTo" />

                        Quick time range - last
                        <a href="#" onclick="$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-1, 'h').format('YYYY-MM-DD HH:mm:00')); return false;">1 hour</a> |
                        <a href="#" onclick="$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-4, 'h').format('YYYY-MM-DD HH:mm:00')); return false;">4 hours</a> |
                        <a href="#" onclick="$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-1, 'd').format('YYYY-MM-DD HH:mm:00')); return false;">1 day</a> |
                        <a href="#" onclick="$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-7, 'd').format('YYYY-MM-DD HH:mm:00')); return false;">7 days</a>

                    </div>
                    <div class="form-group">
                        Search:
                        <input class="form-control" type="text" value="@jobFilter.SearchPhrase" name="searchPhrase" />
                        State:
                        <select class="form-control" name="stateName">
                            @foreach (var option in new string[] { "", "Succeeded", "Processing", "Failed", "Deleted", "Scheduled" })
                            {
                                <option value="@option" @(option == jobFilter.StateName ? "selected=\"selected\"" : "")>@option</option>
                            }
                        </select>
                    </div>

                    <div class="form-group">
                        <input type="submit" value="Filter" class="btn btn-default" />
                    </div>
                </form>
                <br />
                <div>
                    <button class="js-jobs-list-command btn btn-sm btn-primary"
                            data-url="@Url.To("/jobs/failed/requeue")"
                            data-loading-text="@Strings.Common_Enqueueing"
                            disabled="disabled">
                        <span class="glyphicon glyphicon-repeat"></span>
                        @Strings.Common_RequeueJobs
                    </button>
                    <button class="js-jobs-list-command btn btn-sm btn-default"
                            data-url="@Url.To("/jobs/failed/delete")"
                            data-loading-text="@Strings.Common_Deleting"
                            data-confirm="@Strings.Common_DeleteConfirm">
                        <span class="glyphicon glyphicon-remove"></span>
                        @Strings.Common_DeleteSelected
                    </button>
                </div>
            </div>

            <div class="table-responsive">

                <table class="table">
                    <thead>

                        <tr>
                            <th class="min-width">
                                <input type="checkbox" class="js-jobs-list-select-all" />
                            </th>
                            <th class="min-width">@Strings.Common_Id</th>
                            <th colspan="3">@Strings.Common_Job</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var job in jobs)
                        {
                            <tr class="js-jobs-list-row">
                                <td colspan="2">
                                    <input type="checkbox" class="js-jobs-list-checkbox" name="jobs[]" value="@job.Id" />
                                </td>
                                <td>@Html.JobIdLink(job.Id)</td>
                                <td>
                                    <div>
                                        <strong>@job.Method</strong>
                                        <small>@job.Type</small>
                                    </div>
                                    @{
                                        var arr = Newtonsoft.Json.Linq.JArray.Parse(job.Arguments);
                                        var argsText = string.Join(" | ", arr.Values<string>());
                                    }
                                    <textarea disabled style="width: 350px">@argsText</textarea>                                    
                                </td>
                                <td>
                                    <span class="metric @stateNameToClass(job.StateName)">
                                        @job.StateName
                                    </span>
                                </td>
                                <td>
                                    @Html.RelativeTime(job.CreatedAt)
                                </td>
                            </tr>

                        }
                    </tbody>


                </table>

            </div>

        </div>


        <div>
            <h4>Query:</h4>

            <small class="text-muted">
                @extendedMonitoringApi.GetJobFilterAsString(jobFilter)
            </small>
            <br />

            Elapsed: @sw.Elapsed.TotalSeconds.ToString("0.#") s
        </div>
    </div>
</div>
