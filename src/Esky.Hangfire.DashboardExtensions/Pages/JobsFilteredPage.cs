using Esky.Hangfire.DashboardExtensions;
using Hangfire.Dashboard;
using System;
using System.Collections.Generic;
using System.Text;

/// <summary>
/// Page namespace must NOT start with in order to avoid conflicts in razor views with <PERSON>sky.Hangfire namespace
/// </summary>
namespace HangfireExtended.Dashboard
{

    internal partial class JobsFilteredPage  
    {
        public readonly IExtendedMonitoringApi extendedMonitoringApi;

        public JobsFilteredPage(IExtendedMonitoringApi extendedMonitoringApi)
        {
            this.extendedMonitoringApi = extendedMonitoringApi;
        }
    }

    internal partial class JobsGroupedPage
    {
        public readonly IExtendedMonitoringApi extendedMonitoringApi;

        public JobsGroupedPage(IExtendedMonitoringApi extendedMonitoringApi)
        {
            this.extendedMonitoringApi = extendedMonitoringApi;
        }
    }
}
