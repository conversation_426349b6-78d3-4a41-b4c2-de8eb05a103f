using Esky.Hangfire.DashboardExtensions;
using Hangfire.Dashboard;
using HangfireExtended.Dashboard;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.Hangfire.DashboardExtensions
{
    public static class IoC
    {
        public static void UseHangfireExtendedDashboard(this IApplicationBuilder app)
        {
            var extendedMonitoringApi = app.ApplicationServices.GetRequiredService<IExtendedMonitoringApi>();

            DashboardRoutes.Routes.AddRazorPage("/jobs/grouped", x => new JobsGroupedPage(extendedMonitoringApi));

            NavigationMenu.Items.Add(page => new MenuItem("Jobs grouped", page.Url.To("/jobs/grouped")));

            DashboardRoutes.Routes.AddRazorPage("/jobs/filtered", x => new JobsFilteredPage(extendedMonitoringApi));

            NavigationMenu.Items.Add(page => new MenuItem("Jobs filtered", page.Url.To("/jobs/filtered")));
        }
    }
}