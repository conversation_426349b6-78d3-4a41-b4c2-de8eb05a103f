using System;
using System.Text;

namespace Esky.Hangfire.DashboardExtensions
{
    public class JobFilter
    {
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public string StateName { get; set; }
        public string SearchPhrase { get; set; }

        public string AsQueryString()
        {
            var sb = new StringBuilder();
            sb
                .Append("dateFrom=")
                .Append(DateFrom.ToString("yyyy-MM-dd HH:mm:ss"))
                .Append("&dateTo=")
                .Append(DateTo.ToString("yyyy-MM-dd HH:mm:ss"));

            if (!string.IsNullOrWhiteSpace(SearchPhrase))
            {
                sb.Append("&searchPhrase=").Append(SearchPhrase);
            }

            if (!string.IsNullOrWhiteSpace(StateName))
            {
                sb.Append("&stateName=").Append(StateName);
            }

            return sb.ToString();
        }

    }
}
