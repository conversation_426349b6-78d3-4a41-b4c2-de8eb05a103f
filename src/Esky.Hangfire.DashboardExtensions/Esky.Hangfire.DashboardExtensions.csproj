<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <VersionPrefix>1.7.11</VersionPrefix>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire.Core" Version="1.7.32" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="3.1.8" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Pages\JobsFilteredPage.generated.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>JobsFilteredPage.cshtml</DependentUpon>
    </Compile>
    <Compile Update="Pages\JobsGroupedPage.generated.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>JobsGroupedPage.cshtml</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <None Update="Pages\JobsFilteredPage.cshtml">
      <Generator>RazorGenerator</Generator>
      <LastGenOutput>JobsFilteredPage.generated.cs</LastGenOutput>
      <CustomToolNamespace>HangfireExtended.Dashboard</CustomToolNamespace>
    </None>
    <None Update="Pages\JobsGroupedPage.cshtml">
      <LastGenOutput>JobsGroupedPage.generated.cs</LastGenOutput>
      <Generator>RazorGenerator</Generator>
      <CustomToolNamespace>HangfireExtended.Dashboard</CustomToolNamespace>
    </None>
  </ItemGroup>
  
</Project>
