using Hangfire.Dashboard;
using System;

namespace Esky.Hangfire.DashboardExtensions
{
    public static class ModelExtensions
    {
        public static JobFilter OverrideParams(this JobFilter jobFilter, string searchPhrase, string stateName)
        {
            return new JobFilter()
            {
                DateFrom = jobFilter.DateFrom,
                DateTo = jobFilter.DateTo,
                SearchPhrase = searchPhrase,
                StateName = stateName
            };
        }


        public static JobFilter CreateJobFilterFromQueryString(this RazorPage page)
        {
            var jobFilter = new JobFilter()
            {
                DateFrom = DateTime.TryParse(page.Query("dateFrom"), out DateTime dfv)
                    ? dfv
                    : DateTime.UtcNow.AddHours(-1),
                DateTo = DateTime.TryParse(page.Query("dateTo"), out DateTime dtv)
                    ? dtv
                    : DateTime.UtcNow,
                SearchPhrase = page.Query("searchPhrase"),
                StateName = page.Query("stateName")
            };

            return jobFilter;
        }

    }
}
