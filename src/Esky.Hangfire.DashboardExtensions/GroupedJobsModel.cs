using System.Collections.Generic;

namespace Esky.Hangfire.DashboardExtensions
{
    public class GroupedJobsModel
    {
        public IList<JobGroup> JobGroups { get; set; }

        public class JobGroup
        {
            public JobGroupKey Key { get; set; }
            public IList<JobState> States { get; set; }
        }

        public class JobGroupKey
        {
            public string Method { get; set; }
            public string Type { get; set; }
        }

        public class JobState
        {
            public string StateName { get; set; }
            public int Count { get; set; }
        }

    }
}
