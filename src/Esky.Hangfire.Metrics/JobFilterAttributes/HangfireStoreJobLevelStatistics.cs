using System;
using Esky.Hangfire.CustomJobNames;
using Hangfire.Common;
using Hangfire.States;

namespace Esky.Hangfire.Metrics
{
    public sealed class HangfireStoreJobLevelStatistics : JobFilterAttribute, IElectStateFilter
    {
        public HangfireStoreJobLevelStatistics()
        {
            Order = 25;
        }

        public void OnStateElection(ElectStateContext context)
        {
            if (!(context.CandidateState.IsFinal || context.CandidateState is FailedState)) return;

            string stateName = context.CandidateState.Name.ToLower();

            string jobName = context.BackgroundJob.Job.GetJobName();
            
            context.Transaction.AddToSet("jobNames", jobName);

            context.Transaction.IncrementCounter($"jobstats:{stateName}:{jobName}");

            context.Transaction.IncrementCounter(
                $"jobstats:{stateName}:{jobName}:{DateTime.UtcNow:yyyy-MM-dd}",
                DateTime.UtcNow.AddMonths(1) - DateTime.UtcNow);

            context.Transaction.IncrementCounter(
                $"jobstats:{stateName}:{jobName}:{DateTime.UtcNow:yyyy-MM-dd-HH}",
                TimeSpan.FromDays(1));
        }
    }
}
