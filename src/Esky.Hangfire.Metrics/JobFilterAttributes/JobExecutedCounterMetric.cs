using App.Metrics;
using App.Metrics.Counter;
using Esky.Hangfire.CustomJobNames;
using Hangfire.Common;
using Hangfire.States;

namespace Esky.Hangfire.Metrics.JobFilterAttributes
{
    public sealed class JobExecutedCounterMetric : JobFilterAttribute, IElectStateFilter
    {
        private readonly IMetrics _metrics;
        public JobExecutedCounterMetric(IMetrics metrics)
        {
            _metrics = metrics;
            Order = 30;
        }
        
        private static readonly CounterOptions JobsExecutionCounter = new CounterOptions
        {
            Name = "Jobs executed counter"
        };

        public void OnStateElection(ElectStateContext context)
        {
            if (!(context.CandidateState.IsFinal || context.CandidateState is FailedState)) return;

            string stateName = context.CandidateState.Name;

            string jobName = context.BackgroundJob.Job.GetJobName();
            
            var tags = new MetricTags(
                keys: new[] {"job", "state"},
                values: new[] {jobName, stateName});

            var customTags = context.BackgroundJob.Job.GetJobTags();

            tags = MetricTags.Concat(tags, customTags);

            _metrics.Measure.Counter.Increment(JobsExecutionCounter, tags);
        }
    }
}