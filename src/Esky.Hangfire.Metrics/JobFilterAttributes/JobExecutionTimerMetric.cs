using App.Metrics;
using App.Metrics.Timer;
using Esky.Hangfire.CustomJobNames;
using Hangfire.Common;
using Hangfire.Server;

namespace Esky.Hangfire.Metrics.JobFilterAttributes
{
    public sealed class JobExecutionTimerMetric : JobFilterAttribute, IServerFilter
    {
        private readonly IMetrics _metrics;
        public JobExecutionTimerMetric(IMetrics metrics)
        {
            _metrics = metrics;
            Order = 30;
        }

        private static readonly TimerOptions JobExecutionTimer = new TimerOptions
        {
            Name = "Jobs execution time",
        };

        public void OnPerforming(PerformingContext context)
        {
            string jobName = context.BackgroundJob.Job.GetJobName();;

            var tags = new MetricTags(
                keys: new[] {"job"},
                values: new[] {jobName});

            context.Items["timer"] = _metrics.Measure.Timer.Time(JobExecutionTimer);
        }

        public void OnPerformed(PerformedContext context)
        {
            ((TimerContext) context.Items["timer"]).Dispose();
        }
    }
}