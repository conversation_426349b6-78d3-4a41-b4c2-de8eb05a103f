using System;
using System.Linq;
using App.Metrics;
using App.Metrics.AspNetCore;
using App.Metrics.AspNetCore.Endpoints;
using App.Metrics.Formatters.Prometheus;
using Esky.Hangfire.Metrics.JobFilterAttributes;
using Hangfire;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;

namespace Esky.Hangfire.Metrics
{
    public static class HostBuilderExtensions
    {
        public static IHostBuilder ConfigureHangfirePrometheusMetrics(this IHostBuilder hostBuilder)
        {
            var metrics = RegisterMetricsRoot();

            return hostBuilder.ConfigureMetrics(metrics)
                .UseMetrics(options => ConfigureMetricsHost(options, metrics))
                .ConfigureAppMetricsHostingConfiguration(ConfigureMetricsEndpoint);
        }

        public static IWebHostBuilder ConfigureHangfirePrometheusMetrics(this IWebHostBuilder hostBuilder)
        {
            var metrics = RegisterMetricsRoot();

            return hostBuilder.ConfigureMetrics(metrics)
                .UseMetrics(options => ConfigureMetricsHost(options, metrics))
                .ConfigureAppMetricsHostingConfiguration(ConfigureMetricsEndpoint);
        }

        private static void ConfigureMetricsEndpoint(MetricsEndpointsHostingOptions options)
        {
            options.MetricsTextEndpoint = "/metrics";
            options.MetricsEndpoint = "/metrics-protobuf";
        }

        private static void ConfigureMetricsHost(MetricsWebHostOptions options, IMetricsRoot metrics)
        {
            options.EndpointOptions = endpointsOptions =>
            {
                endpointsOptions.MetricsEndpointOutputFormatter = metrics.OutputMetricsFormatters.OfType<MetricsPrometheusProtobufOutputFormatter>().First();
                endpointsOptions.MetricsTextEndpointOutputFormatter = new AsyncMetricsPrometheusTextOutputFormatter();
                endpointsOptions.MetricsTextEndpointEnabled = true;
                endpointsOptions.MetricsEndpointEnabled = true;
            };
        }

        private static IMetricsRoot RegisterMetricsRoot()
        {
            var metrics = AppMetrics.CreateDefaultBuilder()
                .OutputMetrics.AsPrometheusPlainText()
                .OutputMetrics.AsPrometheusProtobuf()
                .Build();

            GlobalJobFilters.Filters.Add(new JobExecutedCounterMetric(metrics));
            GlobalJobFilters.Filters.Add(new JobExecutionTimerMetric(metrics));
            GlobalJobFilters.Filters.Add(new HangfireStoreJobLevelStatistics());

            return metrics;
        }


    }
}