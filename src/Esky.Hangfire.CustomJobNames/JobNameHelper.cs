using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Esky.Hangfire.CustomJobNames.Tags;
using Hangfire;
using Hangfire.Common;

namespace Esky.Hangfire.CustomJobNames
{
    public static class JobNameHelper
    {
        public static string GetJobName(this Job job)
        {
            string jobDisplayName = job.Method.GetCustomAttribute<JobDisplayNameAttribute>()?.DisplayName;

            if (jobDisplayName == null) return job.ToString();

            try
            {
                return string.Format(jobDisplayName, job.Args.ToArray());
            }
            catch (FormatException)
            {
                return jobDisplayName;
            }
        }

        public static Dictionary<string,string> GetJobTags(this Job job)
        {
            var attributes = job.Method.GetCustomAttributes<JobTagAttribute>();
            var result = attributes.ToDictionary(a => a.TagName, a => a.GetTagValue(job));

            return result;
        }
    }
}