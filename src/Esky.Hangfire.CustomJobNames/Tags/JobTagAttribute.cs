using Hangfire.Common;
using System;

namespace Esky.Hangfire.CustomJobNames.Tags
{
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public abstract class JobTagAttribute : Attribute
    {
        public JobTagAttribute(string tagName)
        {
            this.TagName = tagName;
        }

        public abstract string GetTagValue(Job job);

        public string TagName { get; }
    }
}