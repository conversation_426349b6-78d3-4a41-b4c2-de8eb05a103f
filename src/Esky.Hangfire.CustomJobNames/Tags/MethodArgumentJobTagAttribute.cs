using Hangfire.Common;
using System;

namespace Esky.Hangfire.CustomJobNames.Tags
{
    /// <summary>
    /// Add tag with value from job method argument
    /// </summary>
    public class MethodArgumentJobTagAttribute : JobTagAttribute
    {
        public int ArgumentIndex { get; }
        public string StringFormat { get; }
        public IFormatProvider FormatProvider { get; }

        public MethodArgumentJobTagAttribute(string tagName, int argumentIndex, string stringFormat = null, Type formatProvider = null) : base(tagName)
        {
            ArgumentIndex = argumentIndex;
            StringFormat = stringFormat;
            FormatProvider = formatProvider == null ? null : (IFormatProvider) Activator.CreateInstance(formatProvider);
        }

        public override string GetTagValue(Job job)
        {
            var argValue = job.Args[ArgumentIndex];

            return FormatProvider != null
                ? String.Format(FormatProvider, StringFormat ?? "{0}", argValue)
                : String.Format(StringFormat ?? "{0}", argValue);
        }
    }
}