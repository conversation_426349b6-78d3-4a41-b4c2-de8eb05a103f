using Hangfire.Common;

namespace Esky.Hangfire.CustomJobNames.Tags
{
    /// <summary>
    /// Add tag with constant value
    /// </summary>
    public class ConstJobTagAttribute : JobTagAttribute
    {
        public string TagValue { get; }

        public ConstJobTagAttribute(string tagName, string tagValue) : base(tagName)
        {
            this.TagValue = tagValue;
        }

        public override string GetTagValue(Job job)
        {
            return TagValue;
        }
    }
}