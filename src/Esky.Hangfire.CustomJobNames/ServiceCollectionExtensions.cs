using System;
using Hangfire;
using Hangfire.Annotations;
using Microsoft.AspNetCore.Builder;

namespace Esky.Hangfire.CustomJobNames
{
    public static class ServiceCollectionExtensions
    {
        public static IApplicationBuilder UseHangfirePersistJobName(
            [NotNull] this IApplicationBuilder app)
        {
            if (app == null) throw new ArgumentNullException(nameof(app));

            GlobalJobFilters.Filters.Add(new PersistJobNameFilterAttribute());

            return app;
        }
    }
}
