using Hangfire.Client;
using Hangfire.Common;

namespace Esky.Hangfire.CustomJobNames
{
    public sealed class PersistJobNameFilterAttribute : JobFilterAttribute, IClientFilter
    {
        public PersistJobNameFilterAttribute()
        {
            Order = 1;
        }
        public void OnCreating(CreatingContext filterContext)
        {
            filterContext.SetJobParameter("JobName", filterContext.Job.GetJobName());
        }

        public void OnCreated(CreatedContext filterContext)
        {
            
        }
    }
}