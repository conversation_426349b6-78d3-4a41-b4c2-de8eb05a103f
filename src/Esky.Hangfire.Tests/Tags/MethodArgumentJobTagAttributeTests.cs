using Esky.Hangfire.CustomJobNames.Tags;
using Hangfire.Common;
using System;
using System.Linq;
using System.Reflection;
using Xunit;

namespace Esky.Hangfire.Tests
{
    public class MethodArgumentJobTagAttributeTests
    {
        [Theory]
        [InlineData("arg1Value", "arg2paramValue")]
        [InlineData(null, "arg2paramValue")]
        [InlineData("arg1Value", null)]
        public void MethodArgumentJobTag_ShouldReturnSpecifiedTagValue(string arg1Value, string arg2ParamValue)
        {
            var method = typeof(JobMock).GetMethod("Execute");
            var arguments = new object[] { arg1Value, new JobMock.CustomParam { Value = arg2ParamValue } };
            var job = new Job(method, arguments);
            
            var attributes = job.Method.GetCustomAttributes<JobTagAttribute>();

            Assert.Equal($"{arg1Value}", attributes.Single(a => a.TagName == "Custom1").GetTagValue(job));
            Assert.Equal($"-{arg1Value}-", attributes.Single(a => a.TagName == "Custom2").GetTagValue(job));
            Assert.Equal($"{arg2ParamValue}", attributes.Single(a => a.TagName == "Custom3").GetTagValue(job));
        }

        public class JobMock
        {
            [MethodArgumentJobTag("Custom1", 0)]
            [MethodArgumentJobTag("Custom2", 0, stringFormat: "-{0}-")]
            [MethodArgumentJobTag("Custom3", 1, formatProvider: typeof(CustomParamFormatter))]
            public void Execute(string arg1, CustomParam arg2)
            {
            }

            public class CustomParam
            {
                public string Value { get; set; }
            }

            public class CustomParamFormatter : IFormatProvider, ICustomFormatter
            {
                public static IFormatProvider Default = new CustomParamFormatter();

                public string Format(string format, object arg, IFormatProvider formatProvider)
                {
                    return (arg as CustomParam)?.Value ?? "";
                }

                public object GetFormat(Type formatType)
                {
                    return formatType == typeof(ICustomFormatter) ? this : null;
                }
            }
        }
    }
}
