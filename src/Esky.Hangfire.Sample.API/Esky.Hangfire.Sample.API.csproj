<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <AssemblyName>Esky.Hangfire.Sample.API</AssemblyName>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="bootstrap" Version="4.4.1" />
    <PackageReference Include="Hangfire.AspNetCore" Version="1.7.32" />
    <PackageReference Include="Hangfire.Core" Version="1.7.32" />
    <PackageReference Include="jquery" Version="3.4.1" />
    <PackageReference Include="Microsoft.VisualStudio.Web.BrowserLink" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="3.1.7" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Esky.Hangfire.CustomJobNames\Esky.Hangfire.CustomJobNames.csproj" />
    <ProjectReference Include="..\Esky.Hangfire.Metrics\Esky.Hangfire.Metrics.csproj" />
    <ProjectReference Include="..\Esky.Hangfire.DashboardExtensions.Mongo\Esky.Hangfire.DashboardExtensions.Mongo.csproj" />
    <ProjectReference Include="..\Esky.Hangfire.DashboardExtensions\Esky.Hangfire.DashboardExtensions.csproj" />
  </ItemGroup>
</Project>
