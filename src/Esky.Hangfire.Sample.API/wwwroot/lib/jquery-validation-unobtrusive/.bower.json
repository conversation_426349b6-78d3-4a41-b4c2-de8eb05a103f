{"name": "jquery-validation-unobtrusive", "version": "3.2.6", "homepage": "https://github.com/aspnet/jquery-validation-unobtrusive", "description": "Add-on to jQuery Validation to enable unobtrusive validation options in data-* attributes.", "main": ["jquery.validate.unobtrusive.js"], "ignore": ["**/.*", "*.json", "*.md", "*.txt", "gulpfile.js"], "keywords": ["j<PERSON>y", "asp.net", "mvc", "validation", "unobtrusive"], "authors": ["Microsoft"], "license": "http://www.microsoft.com/web/webpi/eula/net_library_eula_enu.htm", "repository": {"type": "git", "url": "git://github.com/aspnet/jquery-validation-unobtrusive.git"}, "dependencies": {"jquery-validation": ">=1.8", "jquery": ">=1.8"}, "_release": "3.2.6", "_resolution": {"type": "version", "tag": "v3.2.6", "commit": "13386cd1b5947d8a5d23a12b531ce3960be1eba7"}, "_source": "git://github.com/aspnet/jquery-validation-unobtrusive.git", "_target": "3.2.6", "_originalSource": "jquery-validation-unobtrusive"}