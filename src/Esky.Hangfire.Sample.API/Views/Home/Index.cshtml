@{
    ViewBag.Title = "Home Page";
}

<div class="jumbotron">
    <h1>Esky.Hangfire.Sample.API</h1>
    <p><a href="/hangfire/" class="btn btn-primary btn-lg">Dashboard &raquo;</a></p>
    <p><a href="/metrics" class="btn btn-primary btn-lg">Metrics &raquo;</a></p>
    <p><a href="/metrics-protobuf" class="btn btn-primary btn-lg">Metrics-protobuf &raquo;</a></p>
</div>

<div class="row">
    <div class="col-md-4">
        <h2>Fire-and-forget tasks</h2>
        <p><a class="btn btn-success" href="@Url.Action("FireAndForget", new {id = 1})">Run 1 task &raquo;</a></p>
        <p><a class="btn btn-success" href="@Url.Action("FireAndForget", new {id = 10})">Run 10 task &raquo;</a></p>
        <p><a class="btn btn-success" href="@Url.Action("FireAndForget", new {id = 100})">Run 100 task &raquo;</a></p>
        <p><a class="btn btn-success" href="@Url.Action("FireAndForget", new {id = 250})">Run 250 task &raquo;</a></p>
        <p><a class="btn btn-success" href="@Url.Action("FireAndForget", new {id = 1000})">Run 1000 task &raquo;</a></p>
    </div>
    <div class="col-md-4">
        <h2>Delayed tasks</h2>
        <p><a class="btn btn-success" href="@Url.Action("Delayed", new {id = 1})">Run 1 task &raquo;</a></p>
        <p><a class="btn btn-success" href="@Url.Action("Delayed", new {id = 10})">Run 10 task &raquo;</a></p>
        <p><a class="btn btn-success" href="@Url.Action("Delayed", new {id = 100})">Run 100 task &raquo;</a></p>
        <p><a class="btn btn-success" href="@Url.Action("Delayed", new {id = 250})">Run 250 task &raquo;</a></p>
        <p><a class="btn btn-success" href="@Url.Action("Delayed", new {id = 1000})">Run 1000 task &raquo;</a></p>
    </div>
    <div class="col-md-4">
        <h2>Recurring tasks</h2>
        <p><a class="btn btn-success" href="@Url.Action("Recurring")">Run &raquo;</a></p>
    </div>
    <div class="col-md-4">
        <h2>Custom display name</h2>
        <p><input class="in-class" id="CustomDisplayNameParam1" type="text" placeholder="Job naming argument"  /><br/>
            <input class="in-class" id="CustomDisplayNameParam2" type="text" placeholder="Ignored naming argument"  /><br />
            <a class="btn btn-success" id="CustomDisplayNameButton" onclick="location.href='@Url.Action("CustomDisplayName", "Home")?namingArg='+$('#CustomDisplayNameParam1').val()+'&amp;ignoredArg='+$('#CustomDisplayNameParam2').val()">Run &raquo;</a></p>
    </div>
</div>