using Esky.Hangfire.CustomJobNames;
using Esky.Hangfire.DashboardExtensions;
using Hangfire;
using Hangfire.Mongo;
using Hangfire.Mongo.Migration.Strategies;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using LogLevel = Hangfire.Logging.LogLevel;

namespace Esky.Hangfire.Sample.API
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            // Add framework services.
            services.AddHangfire(config =>
            {
                
                // Read DefaultConnection string from appsettings.json
                var connectionString = Configuration.GetConnectionString("DefaultConnection");
                var mongoUrlBuilder = new MongoUrlBuilder(connectionString);
                var mongoClient = new MongoClient(mongoUrlBuilder.ToMongoUrl());
                
                var storageOptions = new MongoStorageOptions
                {
                    MigrationOptions = new MongoMigrationOptions()
                    {
                        MigrationStrategy = new MigrateMongoMigrationStrategy()
                    }
                };
                //config.UseLogProvider(new FileLogProvider());
                config.UseColouredConsoleLogProvider(LogLevel.Info);
                config.UseMongoStorage(mongoClient, mongoUrlBuilder.DatabaseName, storageOptions);
            });
            services.AddMongoDashboardExtensions();
            services.AddMvc();

        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostEnvironment env, ILoggerFactory loggerFactory)
        {
            var options = new BackgroundJobServerOptions {Queues = new[] {"default", "notDefault"}};

            app.UseHangfireServer(options);
            app.UseHangfireDashboard();
            app.UseHangfireExtendedDashboard();
            app.UseHangfirePersistJobName();

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseBrowserLink();
            }
            else
            {
                app.UseExceptionHandler("/Home/Error");
            }

            app.UseStaticFiles();

            app.UseRouting();

            app.UseEndpoints(endpoints  =>
            {
                endpoints .MapControllerRoute("default", "{controller=Home}/{action=Index}/{id?}");
            });
        }
    }
}
