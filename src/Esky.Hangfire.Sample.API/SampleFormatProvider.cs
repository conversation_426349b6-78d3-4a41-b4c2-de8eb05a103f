using System;

namespace Esky.Hangfire.Sample.API.Controllers
{
    public class SampleFormatProvider : IFormatProvider, ICustomFormatter
    {
        public static IFormatProvider Default = new SampleFormatProvider();

        public string Format(string format, object arg, IFormatProvider formatProvider)
        {
            return String.Format(format ?? "{0}", $"={arg}=");
        }

        public object GetFormat(Type formatType)
        {
            return formatType == typeof(ICustomFormatter) ? this : null;
        }
    }
}
