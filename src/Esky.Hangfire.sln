
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.30204.135
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Samples", "Samples", "{07E617A3-7D61-4AE2-A1A4-F18686F03D46}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.Hangfire.Sample.API", "Esky.Hangfire.Sample.API\Esky.Hangfire.Sample.API.csproj", "{F7ACBB70-F6B6-4F94-9386-7B589D2491FB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.Hangfire.Metrics", "Esky.Hangfire.Metrics\Esky.Hangfire.Metrics.csproj", "{3DDF24FA-465B-4C8F-ABA2-4EABAAAD0729}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.Hangfire.DashboardExtensions", "Esky.Hangfire.DashboardExtensions\Esky.Hangfire.DashboardExtensions.csproj", "{3F0C8ED4-0B1C-4AD1-B6BA-BA9DFC7C91ED}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "DashboardExtensions", "DashboardExtensions", "{5F0708F7-C1D6-454E-8F9D-FC678E073FA8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.Hangfire.DashboardExtensions.Mongo", "Esky.Hangfire.DashboardExtensions.Mongo\Esky.Hangfire.DashboardExtensions.Mongo.csproj", "{299A81CB-8513-4519-A258-9B44140DDE59}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.Hangfire.CustomJobNames", "Esky.Hangfire.CustomJobNames\Esky.Hangfire.CustomJobNames.csproj", "{6E9FF62D-A3EE-43A8-8A02-02A7CCCF1C18}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Esky.Hangfire.Tests", "Esky.Hangfire.Tests\Esky.Hangfire.Tests.csproj", "{D34CA78B-7D5F-4440-9C1C-F8B1EBFFB01D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F7ACBB70-F6B6-4F94-9386-7B589D2491FB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7ACBB70-F6B6-4F94-9386-7B589D2491FB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7ACBB70-F6B6-4F94-9386-7B589D2491FB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7ACBB70-F6B6-4F94-9386-7B589D2491FB}.Release|Any CPU.Build.0 = Release|Any CPU
		{3DDF24FA-465B-4C8F-ABA2-4EABAAAD0729}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3DDF24FA-465B-4C8F-ABA2-4EABAAAD0729}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3DDF24FA-465B-4C8F-ABA2-4EABAAAD0729}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3DDF24FA-465B-4C8F-ABA2-4EABAAAD0729}.Release|Any CPU.Build.0 = Release|Any CPU
		{3F0C8ED4-0B1C-4AD1-B6BA-BA9DFC7C91ED}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3F0C8ED4-0B1C-4AD1-B6BA-BA9DFC7C91ED}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3F0C8ED4-0B1C-4AD1-B6BA-BA9DFC7C91ED}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3F0C8ED4-0B1C-4AD1-B6BA-BA9DFC7C91ED}.Release|Any CPU.Build.0 = Release|Any CPU
		{299A81CB-8513-4519-A258-9B44140DDE59}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{299A81CB-8513-4519-A258-9B44140DDE59}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{299A81CB-8513-4519-A258-9B44140DDE59}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{299A81CB-8513-4519-A258-9B44140DDE59}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E9FF62D-A3EE-43A8-8A02-02A7CCCF1C18}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E9FF62D-A3EE-43A8-8A02-02A7CCCF1C18}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E9FF62D-A3EE-43A8-8A02-02A7CCCF1C18}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E9FF62D-A3EE-43A8-8A02-02A7CCCF1C18}.Release|Any CPU.Build.0 = Release|Any CPU
		{D34CA78B-7D5F-4440-9C1C-F8B1EBFFB01D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D34CA78B-7D5F-4440-9C1C-F8B1EBFFB01D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D34CA78B-7D5F-4440-9C1C-F8B1EBFFB01D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D34CA78B-7D5F-4440-9C1C-F8B1EBFFB01D}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F7ACBB70-F6B6-4F94-9386-7B589D2491FB} = {07E617A3-7D61-4AE2-A1A4-F18686F03D46}
		{3F0C8ED4-0B1C-4AD1-B6BA-BA9DFC7C91ED} = {5F0708F7-C1D6-454E-8F9D-FC678E073FA8}
		{299A81CB-8513-4519-A258-9B44140DDE59} = {5F0708F7-C1D6-454E-8F9D-FC678E073FA8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9891681B-51BA-4C71-89B3-ECEC64758A11}
	EndGlobalSection
EndGlobal
