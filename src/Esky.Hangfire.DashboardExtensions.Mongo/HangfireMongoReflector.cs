using Hangfire;
using Hangfire.Mongo.Database;
using Hangfire.Mongo.Dto;
using MongoDB.Driver;
using System.Reflection;
using System.Threading.Tasks;

namespace Esky.Hangfire.DashboardExtensions.Mongo
{
    public class HangfireMongoReflector
    {
        private static HangfireDbContext _hangfireDbContext;
        public static HangfireDbContext HangfireDbContext
        {
            get
            {
                if (_hangfireDbContext == null)
                {
                    var field = JobStorage.Current.GetType().GetField("_dbContext", BindingFlags.Instance | BindingFlags.NonPublic)
                        ?? JobStorage.Current.GetType().GetField("HangfireDbContext", BindingFlags.Instance | BindingFlags.NonPublic);

                    _hangfireDbContext = (HangfireDbContext)field.GetValue(JobStorage.Current);
                }
                return _hangfireDbContext;

            }
        }

        private static IMongoCollection<JobDto> _jobGraph;
        public static IMongoCollection<JobDto> JobGraph
        {
            get
            {
                if (_jobGraph == null)
                {
                    var dbContext = HangfireDbContext;
                    var prefix = dbContext.GetType().GetField("_prefix", BindingFlags.Instance | BindingFlags.NonPublic).GetValue(dbContext).ToString();

                    // for aggregation JobDto collection is required:
                    _jobGraph = dbContext.JobGraph.Database.GetCollection<JobDto>(prefix + ".jobGraph");
                }
                return _jobGraph;
            }
        }
    }
}
