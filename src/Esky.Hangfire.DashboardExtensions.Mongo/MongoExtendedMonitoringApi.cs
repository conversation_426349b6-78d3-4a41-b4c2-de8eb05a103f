using Esky.Hangfire.DashboardExtensions;
using Hangfire.Mongo.Dto;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.Hangfire.DashboardExtensions.Mongo
{
    public class MongoExtendedMonitoringApi : IExtendedMonitoringApi
    {
        IMongoCollection<JobDto> JobGraph => HangfireMongoReflector.JobGraph;

        public IList<JobModel> GetJobs(JobFilter jobFilter)
        {
            return JobGraph.Find(jobFilter.AsBsonDocument())
                .Limit(1000)
                .ToList()
                .Select(ToJobModel)
                .ToList();
        }

        private JobModel ToJobModel(JobDto job)
        {
            var match = System.Text.RegularExpressions.Regex.Match(job.InvocationData, "Type\":\".*?\\.(.*?),.*?\"Method\":\"(.*?)\"");
            var type = match.Groups[1];
            var method = match.Groups[2]; ;
            return new JobModel
            {
                Type = type.ToString(),
                Method = method.ToString(),
                CreatedAt = job.CreatedAt,
                Id = job.Id.ToString(),
                InvocationData = job.InvocationData,
                Arguments = job.Arguments,
                StateName = job.StateName
            };
        }

        
        public GroupedJobsModel GetGroupedJobs(JobFilter jobFilter)
        {

            var match = new BsonDocument
            {
                {
                    "$match",   jobFilter.AsBsonDocument()
                }
            };

            var project = BsonDocument.Parse(
                @"{ 
                    $project : {
                    InvocationData: { $substr: [ ""$InvocationData"", 0, 300 ] },
                    CreatedAt: 1,
                    StateName: 1
                } }"
                );


            var aggregation = JobGraph.Aggregate(PipelineDefinition<JobDto, JobDto>.Create(match, project));

            var jobs = aggregation.ToList()
              .Select(ToJobModel)
              .ToList();

            return new GroupedJobsModel
            {
                JobGroups = jobs
                    .GroupBy(job => new { job.Method, job.Type })
                    .Select(jobGrouping => new GroupedJobsModel.JobGroup()
                    {
                        Key = new GroupedJobsModel.JobGroupKey()
                        {
                            Method = jobGrouping.Key.Method,
                            Type = jobGrouping.Key.Type,
                        },                        
                        States = jobGrouping.GroupBy(jg => jg.StateName)
                                            .Select(stateGrouping => new GroupedJobsModel.JobState
                                            {
                                                Count = stateGrouping.Count(),
                                                StateName = stateGrouping.Key
                                            }).ToList()
                    }).ToList()
            };
        }

        public string GetJobFilterAsString(JobFilter jobFilter)
        {
            return jobFilter.AsBsonDocument().ToString();
        }
    }
}
