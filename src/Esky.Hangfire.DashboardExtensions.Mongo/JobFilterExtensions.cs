using Esky.Hangfire.DashboardExtensions;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Text;

namespace Esky.Hangfire.DashboardExtensions.Mongo
{

    internal static class JobFilterExtensions
    {
        public static BsonDocument AsBsonDocument(this JobFilter jobFilter)
        {
            
            var query = new BsonDocument
            {
                {
                    "_id", new BsonDocument
                    {
                        { "$gt", ObjectId.GenerateNewId(jobFilter.DateFrom) },
                        { "$lt", ObjectId.GenerateNewId(jobFilter.DateTo) }
                    }
                },
                { "_t", "JobDto" }
            };


            if (!string.IsNullOrWhiteSpace(jobFilter.SearchPhrase))
            {
                query.Add(new BsonDocument
                {
                    {  "$or", new BsonArray()
                    {
                        new BsonDocument() { { "InvocationData", new BsonRegularExpression(jobFilter.SearchPhrase) } },
                        new BsonDocument() { { "Arguments", new BsonRegularExpression(jobFilter.SearchPhrase) } }
                    }
                    }
                });
            };


            if (!string.IsNullOrWhiteSpace(jobFilter.StateName))
            {
                query.Add("StateName", jobFilter.StateName);
            }

            return query;
        }
    }
}
