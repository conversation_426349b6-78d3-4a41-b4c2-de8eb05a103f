using Microsoft.Extensions.DependencyInjection;
using System.Runtime.CompilerServices;

namespace Esky.Hangfire.DashboardExtensions
{
    public static class IoC
    {
        public static IServiceCollection AddMongoDashboardExtensions(this IServiceCollection services)
        {
            services.AddSingleton<IExtendedMonitoringApi, Mongo.MongoExtendedMonitoringApi>();

            return services;
        }
    }
}
