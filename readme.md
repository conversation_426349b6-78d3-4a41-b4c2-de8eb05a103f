# Esky.Hangfire.Extensions
### Esky.Hangifire.DashboardExtensions
Register in Startup file in ConfigureServices() method while adding framework services
```c
    public void ConfigureServices(IServiceCollection services)
    {
        // Add framework services.
        services.AddHangfire(config =>
        {
            (...)
        });
        services.AddMongoDashboardExtensions();
        (...)
    }
```
and in Configure() method
```c
    public void Configure(IApplicationBuilder app, IHostEnvironment env, ILoggerFactory loggerFactory)
    {
        (...)
        app.UseHangfireServer(options);
        app.UseHangfireDashboard();
        app.UseHangfireExtendedDashboard();
        (...)
    }
```
### Esky.Hangfire.Metrics
Usage example:
```c
    public static IHostBuilder CreateHostBuilder(string[] args) 
    {
        Host.CreateDefaultBuilder(args)
            .ConfigureWebHostDefaults(webBuilder =>
            {
                webBuilder.UseStartup<Startup>();
            })
            .ConfigureHangfirePrometheusMetrics();
    }
```
To change default job name in the Hangfire dashboard, use `Hangfire.JobDisplayNameAttribute` on method. The most important fact is that provided string is used by `string.Format` with provided actual arguments.
```csharp
    [JobDisplayName("CustomJobName(\"{0}\")")]
    public void ExecuteCustom(string arg0, string arg1)
    {
    }
    // BackgroundJob.Enqueue<JobHandler>(j => j.ExecuteCustom("A", "B");
    // It will be displayed as in the dashboard as CustomJobName("A")
```
![Hangifire Dashoard](content/dashboard_jobdisplayname.png)<br>
`JobDisplayNameAttribute` is respected by `Esky.Hangfire.Metrcis` and is added to `job` tag metric.<br>

#### ConstJobTag attribute
Usage example:
```csharp
            [ConstJobTag("jobType", "Producer")]
            public void Execute(ProviderNameEnum providerName, string bookingId)
            {
            }
```

#### MethodArgumentJobTag attribute
Usage example:
```csharp
            [MethodArgumentJobTag("provider", 0)]
            [MethodArgumentJobTag("someTag", 1)]
            public void Execute(ProviderNameEnum providerName, string someArg)
            {
            }
```

To provide custom argument format, `stringFormat` and/or `IFormatProvider` type can be provided as attribute params:

```csharp
            [MethodArgumentJobTag("provider", 0, formatProvider: typeof(SampleFormatProvider))]
            public void Execute(ProviderNameEnum providerName)
            {
            }

// ...
      public class SampleFormatProvider : IFormatProvider, ICustomFormatter
      {
         public static IFormatProvider Default = new SampleFormatProvider();

         public string Format(string format, object arg, IFormatProvider formatProvider)
         {
            return String.Format(format ?? "{0}", $"={arg}=");
         }

         public object GetFormat(Type formatType)
         {
            return formatType == typeof(ICustomFormatter) ? this : null;
         }
      }
```

Adding both attributes (`ConstJobTag`, `MethodArgumentJobTag)` Will result with following output in Prometheus-formatted metrics:

```
# TYPE application_jobs_executed_counter counter
application_jobs_executed_counter{job="Execute",state="Succeeded",jobType="Producer",provider="Amadeus",server="KAT-IT-1042",app="Esky.Hangfire.Sample.API",env="development"} 1

```

### Esky.Hangfire.CustomJobNames
To store dashboard display names in Hangfire database store, use this extension and register Filter attibute globally or use `ServiceCollectionExtensions` method.
```c
    public void Configure(IApplicationBuilder app, IHostEnvironment env, ILoggerFactory loggerFactory)
    {
        (...)
        app.UseHangfireServer(options);
        app.UseHangfirePersistJobName();
        (...)
    }

    // OR

    Hangfire.GlobalJobFilters.Filters.Add(new PersistJobNameFilterAttribute());
```
For MongoDB Hangfire store it is stored in `Parametrs.JobName` fieled inside `hangfire.jobGraph` collection and allows for easy quering job by name or using that filed in grouping.
```javascript
db.getCollection("hangfire.jobGraph").find({
    "_t":"JobDto", 
    "Parameters.JobName": '"HomeController.PrintToDebug"'
})
// or even using regex
db.getCollection("hangfire.jobGraph").find({
    "_t":"JobDto", 
    "Parameters.JobName": /^"CustomJobName/
})
```
***
# Samples
### Esky.Hangfire.Sample.API
Sample aplication with Hangfire to demonstrate the extensions.<br>
![Sample API](content/sample_api.png)
